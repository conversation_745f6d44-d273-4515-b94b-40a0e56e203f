package com.touptek.measurerealize.utils

import android.graphics.PointF
import android.util.Log
import android.widget.ImageView
import java.util.*
import kotlin.math.*

/**
 * 🎯 三点双圆测量实例 - 基于三点圆算法的双圆数据结构
 * 控制点配置：[内圆点1, 内圆点2, 内圆点3, 外圆半径点, 计算圆心]
 * 核心逻辑：通过三个内圆控制点计算圆心，外圆与内圆同心
 */
data class ThreeTwoCirclesMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [内圆点1, 内圆点2, 内圆点3, 外圆半径点, 计算圆心] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [内圆点1, 内圆点2, 内圆点3, 外圆半径点, 计算圆心] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis(),
    // 缓存计算结果以提高性能
    private var cachedInnerRadius: Double? = null,
    private var cachedOuterRadius: Double? = null,
    private var cachedInnerArea: Double? = null,
    private var cachedOuterArea: Double? = null,
    private var cachedRingArea: Double? = null,
    private var cachedInnerPerimeter: Double? = null,
    private var cachedOuterPerimeter: Double? = null,
    private var cacheValidTime: Long = 0L,
    // 三点圆算法缓存
    private var lastThreePointsHash: Int = 0,
    private var cachedCircleResult: Triple<PointF, Float, Boolean>? = null
) {
    companion object {
        private const val CACHE_VALIDITY_MS = 100L // 缓存有效期100ms
        private const val PI = Math.PI
    }

    /**
     * 🎯 根据三个内圆点计算圆心和半径 - 使用视图坐标（基于ThreeCircleMeasureHelper算法）
     */
    fun calculateInnerCircleFromThreePoints(): Triple<PointF, Float, Boolean> {
        if (viewPoints.size < 3) return Triple(PointF(), 0f, false)

        // 计算三点的哈希值用于缓存
        val currentHash = (viewPoints[0].hashCode() * 31 +
                          viewPoints[1].hashCode()) * 31 +
                          viewPoints[2].hashCode()

        // 如果三点位置没变，返回缓存结果
        if (currentHash == lastThreePointsHash && cachedCircleResult != null) {
            return cachedCircleResult!!
        }

        val p1 = viewPoints[0]  // 内圆点1
        val p2 = viewPoints[1]  // 内圆点2
        val p3 = viewPoints[2]  // 内圆点3

        // 重新计算并更新缓存
        val result = calculateCircleFromThreePoints(p1, p2, p3)
        lastThreePointsHash = currentHash
        cachedCircleResult = result

        return result
    }

    /**
     * 🎯 三点确定圆心算法 - 基于ThreeCircleMeasureHelper实现
     */
    private fun calculateCircleFromThreePoints(p1: PointF, p2: PointF, p3: PointF): Triple<PointF, Float, Boolean> {
        // 检查三点是否共线
        val area = abs((p2.x - p1.x) * (p3.y - p1.y) - (p3.x - p1.x) * (p2.y - p1.y))
        if (area < 1e-6) {
            return Triple(PointF(), 0f, false) // 三点共线，无法确定圆
        }

        // 计算圆心坐标
        val d = 2 * (p1.x * (p2.y - p3.y) + p2.x * (p3.y - p1.y) + p3.x * (p1.y - p2.y))
        if (abs(d) < 1e-6) {
            return Triple(PointF(), 0f, false)
        }

        val ux = ((p1.x * p1.x + p1.y * p1.y) * (p2.y - p3.y) +
                  (p2.x * p2.x + p2.y * p2.y) * (p3.y - p1.y) +
                  (p3.x * p3.x + p3.y * p3.y) * (p1.y - p2.y)) / d

        val uy = ((p1.x * p1.x + p1.y * p1.y) * (p3.x - p2.x) +
                  (p2.x * p2.x + p2.y * p2.y) * (p1.x - p3.x) +
                  (p3.x * p3.x + p3.y * p3.y) * (p2.x - p1.x)) / d

        val center = PointF(ux, uy)
        val radius = sqrt((center.x - p1.x).pow(2) + (center.y - p1.y).pow(2))

        return Triple(center, radius, true)
    }

    /**
     * 🎯 计算内圆半径（使用位图坐标，基于三点圆算法）
     */
    fun calculateInnerRadius(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedInnerRadius != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedInnerRadius!!
        }

        if (bitmapPoints.size < 3) return 0.0

        // 使用位图坐标的三个内圆点计算半径
        val p1 = bitmapPoints[0]  // 内圆点1
        val p2 = bitmapPoints[1]  // 内圆点2
        val p3 = bitmapPoints[2]  // 内圆点3

        val radius = calculateCircleFromBitmapPoints(p1, p2, p3)

        cachedInnerRadius = radius
        cacheValidTime = currentTime
        return radius
    }

    /**
     * 🎯 基于位图坐标的三点圆半径计算
     */
    private fun calculateCircleFromBitmapPoints(p1: PointF, p2: PointF, p3: PointF): Double {
        // 检查三点是否共线
        val area = abs((p2.x - p1.x) * (p3.y - p1.y) - (p3.x - p1.x) * (p2.y - p1.y))
        if (area < 1e-10) return 0.0 // 三点共线

        // 计算圆心坐标
        val d = 2 * (p1.x * (p2.y - p3.y) + p2.x * (p3.y - p1.y) + p3.x * (p1.y - p2.y))
        if (abs(d) < 1e-10) return 0.0 // 避免除零

        val ux = ((p1.x * p1.x + p1.y * p1.y) * (p2.y - p3.y) +
                  (p2.x * p2.x + p2.y * p2.y) * (p3.y - p1.y) +
                  (p3.x * p3.x + p3.y * p3.y) * (p1.y - p2.y)) / d
        val uy = ((p1.x * p1.x + p1.y * p1.y) * (p3.x - p2.x) +
                  (p2.x * p2.x + p2.y * p2.y) * (p1.x - p3.x) +
                  (p3.x * p3.x + p3.y * p3.y) * (p2.x - p1.x)) / d

        val center = PointF(ux, uy)
        return sqrt((center.x - p1.x).pow(2) + (center.y - p1.y).pow(2)).toDouble()
    }

    /**
     * 🎯 计算外圆半径（使用位图坐标）
     */
    fun calculateOuterRadius(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedOuterRadius != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedOuterRadius!!
        }

        if (bitmapPoints.size < 5) return 0.0

        val center = bitmapPoints[4]     // 计算得出的圆心
        val outerPoint = bitmapPoints[3] // 外圆半径控制点
        val dx = outerPoint.x - center.x
        val dy = outerPoint.y - center.y
        val radius = sqrt(dx * dx + dy * dy).toDouble()

        cachedOuterRadius = radius
        cacheValidTime = currentTime
        return radius
    }

    /**
     * 🎯 计算内圆视图半径（用于绘制，基于三点圆算法）
     */
    fun calculateInnerViewRadius(): Float {
        if (viewPoints.size < 3) return 0f

        val (_, radius, isValid) = calculateInnerCircleFromThreePoints()
        return if (isValid) radius else 0f
    }

    /**
     * 🎯 计算外圆视图半径（用于绘制）
     */
    fun calculateOuterViewRadius(): Float {
        if (viewPoints.size < 5) return 0f

        val center = viewPoints[4]       // 计算得出的圆心
        val outerPoint = viewPoints[3]   // 外圆半径控制点
        val dx = outerPoint.x - center.x
        val dy = outerPoint.y - center.y
        return sqrt(dx * dx + dy * dy)
    }

    /**
     * 🎯 计算内圆面积
     */
    fun calculateInnerArea(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedInnerArea != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedInnerArea!!
        }

        val radius = calculateInnerRadius()
        val area = PI * radius * radius

        cachedInnerArea = area
        return area
    }

    /**
     * 🎯 计算外圆面积
     */
    fun calculateOuterArea(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedOuterArea != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedOuterArea!!
        }

        val radius = calculateOuterRadius()
        val area = PI * radius * radius

        cachedOuterArea = area
        return area
    }

    /**
     * 🎯 计算环形面积
     */
    fun calculateRingArea(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedRingArea != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedRingArea!!
        }

        val outerArea = calculateOuterArea()
        val innerArea = calculateInnerArea()
        val ringArea = outerArea - innerArea

        cachedRingArea = ringArea
        return ringArea
    }

    /**
     * 🎯 计算内圆周长
     */
    fun calculateInnerPerimeter(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedInnerPerimeter != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedInnerPerimeter!!
        }

        val radius = calculateInnerRadius()
        val perimeter = 2.0 * PI * radius

        cachedInnerPerimeter = perimeter
        return perimeter
    }

    /**
     * 🎯 计算外圆周长
     */
    fun calculateOuterPerimeter(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedOuterPerimeter != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedOuterPerimeter!!
        }

        val radius = calculateOuterRadius()
        val perimeter = 2.0 * PI * radius

        cachedOuterPerimeter = perimeter
        return perimeter
    }

    /**
     * 🎯 检查点是否在触摸范围内 - 直接在视图坐标系中检测
     */
    fun isPointInTouchRange(touchPoint: PointF, touchRadius: Float): Boolean {
        return viewPoints.any { point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            distance <= touchRadius
        }
    }

    /**
     * 🎯 检查特定点是否在触摸范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, pointIndex: Int, touchRadius: Float): Boolean {
        if (pointIndex < 0 || pointIndex >= viewPoints.size) return false
        val point = viewPoints[pointIndex]
        val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
        return distance <= touchRadius
    }

    /**
     * 🎯 获取最近的控制点索引
     */
    fun getNearestPointIndex(touchPoint: PointF): Int {
        if (viewPoints.isEmpty()) return -1
        
        var nearestIndex = 0
        var minDistance = Float.MAX_VALUE
        
        viewPoints.forEachIndexed { index, point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = index
            }
        }
        
        return nearestIndex
    }

    /**
     * 🎯 三点双圆约束更新方法 - 基于三点圆算法的核心约束逻辑
     * 控制点索引：[0,1,2]=内圆三点, [3]=外圆半径点, [4]=计算圆心
     */
    fun updateWithThreeTwoCirclesConstraint(pointIndex: Int, newPoint: PointF) {
        when (pointIndex) {
            0, 1, 2 -> { // 内圆三点移动 - 重新计算圆心，外圆跟随
                // 更新拖拽的点
                viewPoints[pointIndex] = newPoint

                // 重新计算内圆圆心和半径
                val (newCenter, newRadius, isValid) = calculateInnerCircleFromThreePoints()

                if (isValid) {
                    // 计算圆心移动的偏移量
                    val oldCenter = if (viewPoints.size > 4) viewPoints[4] else PointF(0f, 0f)
                    val deltaX = newCenter.x - oldCenter.x
                    val deltaY = newCenter.y - oldCenter.y

                    // 更新计算圆心
                    if (viewPoints.size > 4) {
                        viewPoints[4] = newCenter
                    } else {
                        viewPoints.add(newCenter)
                    }

                    // 外圆半径点跟随圆心移动，保持相对距离
                    if (viewPoints.size > 3) {
                        viewPoints[3] = PointF(viewPoints[3].x + deltaX, viewPoints[3].y + deltaY)
                    }

                    // 验证外圆约束
                    validateOuterCircleConstraint()
                } else {
                    // 三点共线或无效，恢复原点位置
                    // 这里可以添加视觉反馈或自动修正逻辑
                    Log.w("ThreeTwoCircles", "⚠️ Three points are collinear, cannot determine circle center")
                    // 可选：自动修正共线点
                    autoCorrectCollinearPoints(pointIndex)
                }
            }
            3 -> { // 外圆半径点移动 - 调整外圆大小，保持同心
                if (viewPoints.size < 5) return

                val center = viewPoints[4] // 计算得出的圆心
                val dx = newPoint.x - center.x
                val dy = newPoint.y - center.y
                val newOuterRadius = sqrt(dx * dx + dy * dy)

                // 计算内圆半径
                val (_, innerRadius, isValid) = calculateInnerCircleFromThreePoints()

                if (isValid && newOuterRadius >= innerRadius + 10f) { // 保持至少10px的间距
                    viewPoints[3] = newPoint
                } else {
                    Log.w("ThreeTwoCircles", "⚠️ Outer radius constraint violated: outer=$newOuterRadius, inner=$innerRadius")
                }
            }
            4 -> { // 圆心拖拽 - 整体平移，保持三点相对位置
                if (viewPoints.size < 5) return

                val oldCenter = viewPoints[4]
                val deltaX = newPoint.x - oldCenter.x
                val deltaY = newPoint.y - oldCenter.y

                // 平移所有控制点
                for (i in 0..3) {
                    viewPoints[i] = PointF(viewPoints[i].x + deltaX, viewPoints[i].y + deltaY)
                }
                viewPoints[4] = newPoint
            }
        }
        invalidateCache()
        markAsModified()
    }

    /**
     * 🎯 验证外圆约束 - 确保外圆半径不小于内圆半径
     */
    private fun validateOuterCircleConstraint() {
        if (viewPoints.size < 5) return

        val center = viewPoints[4]
        val outerPoint = viewPoints[3]
        val (_, innerRadius, isValid) = calculateInnerCircleFromThreePoints()

        if (isValid) {
            val currentOuterRadius = sqrt((outerPoint.x - center.x).pow(2) + (outerPoint.y - center.y).pow(2))
            val minOuterRadius = innerRadius + 10f // 最小间距10px

            if (currentOuterRadius < minOuterRadius) {
                // 自动调整外圆半径点位置
                val angle = atan2(outerPoint.y - center.y, outerPoint.x - center.x)
                viewPoints[3] = PointF(
                    center.x + minOuterRadius * cos(angle),
                    center.y + minOuterRadius * sin(angle)
                )
                Log.d("ThreeTwoCircles", "🔧 Auto-adjusted outer radius to maintain constraint")
            }
        }
    }

    /**
     * 🎯 自动修正共线点 - 轻微偏移使三点不共线
     */
    private fun autoCorrectCollinearPoints(draggedPointIndex: Int) {
        val offset = 20f // 20像素偏移

        // 选择一个不是当前拖拽的点进行偏移
        val targetIndex = when (draggedPointIndex) {
            0 -> 2  // 如果拖拽点0，偏移点2
            1 -> 2  // 如果拖拽点1，偏移点2
            2 -> 1  // 如果拖拽点2，偏移点1
            else -> 2
        }

        if (targetIndex < viewPoints.size) {
            val targetPoint = viewPoints[targetIndex]
            viewPoints[targetIndex] = PointF(targetPoint.x, targetPoint.y + offset)

            // 重新计算圆心
            val (newCenter, _, isValid) = calculateInnerCircleFromThreePoints()
            if (isValid && viewPoints.size > 4) {
                viewPoints[4] = newCenter
            }

            Log.d("ThreeTwoCircles", "🔧 Auto-corrected collinear points by offsetting point $targetIndex")
        }
    }

    /**
     * 🔄 坐标转换方法 - 视图坐标转位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = android.graphics.Matrix()
        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }

    /**
     * 🔄 同步位图坐标 - 将视图坐标转换为位图坐标
     */
    fun syncBitmapCoords(imageView: ImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            bitmapPoints.add(convertViewToBitmapCoords(viewPoint, imageView))
        }
        invalidateCache()
    }

    /**
     * 🎯 计算文本显示位置（基于计算得出的圆心）
     */
    fun calculateTextPosition(): PointF {
        if (viewPoints.size < 5) return PointF(0f, 0f)

        val center = viewPoints[4] // 计算得出的圆心
        val outerRadius = calculateOuterViewRadius()
        return PointF(center.x, center.y + outerRadius + 60f)
    }

    /**
     * 🎯 获取双圆的显示文本
     */
    fun getDisplayText(): String {
        val innerRadius = calculateInnerRadius()
        val outerRadius = calculateOuterRadius()
        val innerArea = calculateInnerArea()
        val outerArea = calculateOuterArea()
        val ringArea = calculateRingArea()
        val innerPerimeter = calculateInnerPerimeter()
        val outerPerimeter = calculateOuterPerimeter()

        return """内圆：周长：%.1f px，面积：%.1f px²
外圆：周长：%.1f px，面积：%.1f px²
环形面积：%.1f px²""".format(innerPerimeter, innerArea, outerPerimeter, outerArea, ringArea)
    }

    /**
     * 🔄 标记为已修改
     */
    private fun markAsModified() {
        lastModified = System.currentTimeMillis()
    }

    /**
     * 🔄 清除缓存
     */
    private fun invalidateCache() {
        cachedInnerRadius = null
        cachedOuterRadius = null
        cachedInnerArea = null
        cachedOuterArea = null
        cachedRingArea = null
        cachedInnerPerimeter = null
        cachedOuterPerimeter = null
        cacheValidTime = 0L
    }
}
