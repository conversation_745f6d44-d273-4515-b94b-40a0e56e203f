package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 三点双圆测量实例 - 基于三点圆算法的双圆数据结构
 * 控制点配置：[内圆点1, 内圆点2, 内圆点3, 外圆半径点, 计算圆心]
 * 核心逻辑：通过三个内圆控制点计算圆心，外圆与内圆同心
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\b\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\b\u001a\n\u0002\u0010\u0002\n\u0002\b(\n\u0002\u0018\u0002\n\u0002\b\u0017\b\u0086\b\u0018\u0000 w2\u00020\u0001:\u0001wB\u00f3\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\t\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0011\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u001a\u0012\u001c\b\u0002\u0010\u001b\u001a\u0016\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\t\u0018\u00010\u001c\u00a2\u0006\u0002\u0010\u001eJ\u0010\u00107\u001a\u0002082\u0006\u00109\u001a\u00020\u001aH\u0002J \u0010:\u001a\u00020\u00112\u0006\u0010;\u001a\u00020\u00062\u0006\u0010<\u001a\u00020\u00062\u0006\u0010=\u001a\u00020\u0006H\u0002J2\u0010>\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\t0\u001c2\u0006\u0010;\u001a\u00020\u00062\u0006\u0010<\u001a\u00020\u00062\u0006\u0010=\u001a\u00020\u0006H\u0002J\u0006\u0010?\u001a\u00020\u0011J\u0018\u0010@\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\t0\u001cJ\u0006\u0010A\u001a\u00020\u0011J\u0006\u0010B\u001a\u00020\u0011J\u0006\u0010C\u001a\u00020\u001dJ\u0006\u0010D\u001a\u00020\u0011J\u0006\u0010E\u001a\u00020\u0011J\u0006\u0010F\u001a\u00020\u0011J\u0006\u0010G\u001a\u00020\u001dJ\u0006\u0010H\u001a\u00020\u0011J\u0006\u0010I\u001a\u00020\u0006J\t\u0010J\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010K\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010LJ\u0010\u0010M\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010LJ\u0010\u0010N\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010LJ\u0010\u0010O\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010LJ\u0010\u0010P\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010LJ\u0010\u0010Q\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010LJ\u0010\u0010R\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010LJ\t\u0010S\u001a\u00020\u000eH\u00c2\u0003J\t\u0010T\u001a\u00020\u001aH\u00c2\u0003J\u001d\u0010U\u001a\u0016\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\t\u0018\u00010\u001cH\u00c2\u0003J\u000f\u0010V\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010W\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010X\u001a\u00020\tH\u00c6\u0003J\t\u0010Y\u001a\u00020\tH\u00c6\u0003J\t\u0010Z\u001a\u00020\tH\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010\\\u001a\u00020\u000eH\u00c6\u0003J\t\u0010]\u001a\u00020\u000eH\u00c6\u0003J\u0018\u0010^\u001a\u00020\u00062\u0006\u0010_\u001a\u00020\u00062\u0006\u0010`\u001a\u00020aH\u0002J\u0018\u0010b\u001a\u00020\u00062\u0006\u0010c\u001a\u00020\u00062\u0006\u0010`\u001a\u00020aH\u0002J\u00fc\u0001\u0010d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000e2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00112\b\b\u0002\u0010\u0018\u001a\u00020\u000e2\b\b\u0002\u0010\u0019\u001a\u00020\u001a2\u001c\b\u0002\u0010\u001b\u001a\u0016\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\t\u0018\u00010\u001cH\u00c6\u0001\u00a2\u0006\u0002\u0010eJ\u0013\u0010f\u001a\u00020\t2\b\u0010g\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010h\u001a\u00020\u0003J\u000e\u0010i\u001a\u00020\u001a2\u0006\u0010j\u001a\u00020\u0006J\t\u0010k\u001a\u00020\u001aH\u00d6\u0001J\b\u0010l\u001a\u000208H\u0002J\u0016\u0010m\u001a\u00020\t2\u0006\u0010j\u001a\u00020\u00062\u0006\u0010n\u001a\u00020\u001dJ\u001e\u0010m\u001a\u00020\t2\u0006\u0010j\u001a\u00020\u00062\u0006\u0010o\u001a\u00020\u001a2\u0006\u0010n\u001a\u00020\u001dJ\b\u0010p\u001a\u000208H\u0002J\u000e\u0010q\u001a\u0002082\u0006\u0010`\u001a\u00020aJ\u000e\u0010r\u001a\u0002082\u0006\u0010`\u001a\u00020aJ\t\u0010s\u001a\u00020\u0003H\u00d6\u0001J\u0016\u0010t\u001a\u0002082\u0006\u0010o\u001a\u00020\u001a2\u0006\u0010u\u001a\u00020\u0006J\b\u0010v\u001a\u000208H\u0002R \u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010 \"\u0004\b!\u0010\"R\u000e\u0010\u0018\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\u001b\u001a\u0016\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\t\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0013\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010#R\u0012\u0010\u0016\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010#R\u0012\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010#R\u0012\u0010\u0014\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010#R\u0012\u0010\u0017\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010#R\u0012\u0010\u0012\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010#R\u0012\u0010\u0015\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010#R\u001a\u0010\r\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010%\"\u0004\b&\u0010\'R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u001a\u0010\u000b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010*\"\u0004\b+\u0010,R\u001a\u0010\n\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010*\"\u0004\b-\u0010,R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010*\"\u0004\b.\u0010,R\u001a\u0010\u000f\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b/\u0010%\"\u0004\b0\u0010\'R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\f\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b1\u00102\"\u0004\b3\u00104R \u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u0010 \"\u0004\b6\u0010\"\u00a8\u0006x"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeTwoCirclesMeasurement;", "", "id", "", "viewPoints", "", "Landroid/graphics/PointF;", "bitmapPoints", "isSelected", "", "isEditing", "isCompleted", "textPosition", "creationTime", "", "lastModified", "cachedInnerRadius", "", "cachedOuterRadius", "cachedInnerArea", "cachedOuterArea", "cachedRingArea", "cachedInnerPerimeter", "cachedOuterPerimeter", "cacheValidTime", "lastThreePointsHash", "", "cachedCircleResult", "Lkotlin/Triple;", "", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;JJLjava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;JILkotlin/Triple;)V", "getBitmapPoints", "()Ljava/util/List;", "setBitmapPoints", "(Ljava/util/List;)V", "Ljava/lang/Double;", "getCreationTime", "()J", "setCreationTime", "(J)V", "getId", "()Ljava/lang/String;", "()Z", "setCompleted", "(Z)V", "setEditing", "setSelected", "getLastModified", "setLastModified", "getTextPosition", "()Landroid/graphics/PointF;", "setTextPosition", "(Landroid/graphics/PointF;)V", "getViewPoints", "setViewPoints", "autoCorrectCollinearPoints", "", "draggedPointIndex", "calculateCircleFromBitmapPoints", "p1", "p2", "p3", "calculateCircleFromThreePoints", "calculateInnerArea", "calculateInnerCircleFromThreePoints", "calculateInnerPerimeter", "calculateInnerRadius", "calculateInnerViewRadius", "calculateOuterArea", "calculateOuterPerimeter", "calculateOuterRadius", "calculateOuterViewRadius", "calculateRingArea", "calculateTextPosition", "component1", "component10", "()Ljava/lang/Double;", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "convertBitmapToViewCoords", "bitmapPoint", "imageView", "Landroid/widget/ImageView;", "convertViewToBitmapCoords", "viewPoint", "copy", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;JJLjava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;JILkotlin/Triple;)Lcom/touptek/measurerealize/utils/ThreeTwoCirclesMeasurement;", "equals", "other", "getDisplayText", "getNearestPointIndex", "touchPoint", "hashCode", "invalidateCache", "isPointInTouchRange", "touchRadius", "pointIndex", "markAsModified", "syncBitmapCoords", "syncViewCoords", "toString", "updateWithThreeTwoCirclesConstraint", "newPoint", "validateOuterCircleConstraint", "Companion", "app_debug"})
public final class ThreeTwoCirclesMeasurement {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> viewPoints;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> bitmapPoints;
    private boolean isSelected;
    private boolean isEditing;
    private boolean isCompleted;
    @org.jetbrains.annotations.Nullable
    private android.graphics.PointF textPosition;
    private long creationTime;
    private long lastModified;
    private java.lang.Double cachedInnerRadius;
    private java.lang.Double cachedOuterRadius;
    private java.lang.Double cachedInnerArea;
    private java.lang.Double cachedOuterArea;
    private java.lang.Double cachedRingArea;
    private java.lang.Double cachedInnerPerimeter;
    private java.lang.Double cachedOuterPerimeter;
    private long cacheValidTime;
    private int lastThreePointsHash;
    private kotlin.Triple<? extends android.graphics.PointF, java.lang.Float, java.lang.Boolean> cachedCircleResult;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.ThreeTwoCirclesMeasurement.Companion Companion = null;
    private static final long CACHE_VALIDITY_MS = 100L;
    private static final double PI = java.lang.Math.PI;
    
    /**
     * 🎯 三点双圆测量实例 - 基于三点圆算法的双圆数据结构
     * 控制点配置：[内圆点1, 内圆点2, 内圆点3, 外圆半径点, 计算圆心]
     * 核心逻辑：通过三个内圆控制点计算圆心，外圆与内圆同心
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.ThreeTwoCirclesMeasurement copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedRingArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerPerimeter, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterPerimeter, long cacheValidTime, int lastThreePointsHash, @org.jetbrains.annotations.Nullable
    kotlin.Triple<? extends android.graphics.PointF, java.lang.Float, java.lang.Boolean> cachedCircleResult) {
        return null;
    }
    
    /**
     * 🎯 三点双圆测量实例 - 基于三点圆算法的双圆数据结构
     * 控制点配置：[内圆点1, 内圆点2, 内圆点3, 外圆半径点, 计算圆心]
     * 核心逻辑：通过三个内圆控制点计算圆心，外圆与内圆同心
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 🎯 三点双圆测量实例 - 基于三点圆算法的双圆数据结构
     * 控制点配置：[内圆点1, 内圆点2, 内圆点3, 外圆半径点, 计算圆心]
     * 核心逻辑：通过三个内圆控制点计算圆心，外圆与内圆同心
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 🎯 三点双圆测量实例 - 基于三点圆算法的双圆数据结构
     * 控制点配置：[内圆点1, 内圆点2, 内圆点3, 外圆半径点, 计算圆心]
     * 核心逻辑：通过三个内圆控制点计算圆心，外圆与内圆同心
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public ThreeTwoCirclesMeasurement() {
        super();
    }
    
    public ThreeTwoCirclesMeasurement(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedRingArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedInnerPerimeter, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedOuterPerimeter, long cacheValidTime, int lastThreePointsHash, @org.jetbrains.annotations.Nullable
    kotlin.Triple<? extends android.graphics.PointF, java.lang.Float, java.lang.Boolean> cachedCircleResult) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getViewPoints() {
        return null;
    }
    
    public final void setViewPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getBitmapPoints() {
        return null;
    }
    
    public final void setBitmapPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean isEditing() {
        return false;
    }
    
    public final void setEditing(boolean p0) {
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean isCompleted() {
        return false;
    }
    
    public final void setCompleted(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF getTextPosition() {
        return null;
    }
    
    public final void setTextPosition(@org.jetbrains.annotations.Nullable
    android.graphics.PointF p0) {
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long getCreationTime() {
        return 0L;
    }
    
    public final void setCreationTime(long p0) {
    }
    
    public final long component9() {
        return 0L;
    }
    
    public final long getLastModified() {
        return 0L;
    }
    
    public final void setLastModified(long p0) {
    }
    
    private final java.lang.Double component10() {
        return null;
    }
    
    private final java.lang.Double component11() {
        return null;
    }
    
    private final java.lang.Double component12() {
        return null;
    }
    
    private final java.lang.Double component13() {
        return null;
    }
    
    private final java.lang.Double component14() {
        return null;
    }
    
    private final java.lang.Double component15() {
        return null;
    }
    
    private final java.lang.Double component16() {
        return null;
    }
    
    private final long component17() {
        return 0L;
    }
    
    private final int component18() {
        return 0;
    }
    
    private final kotlin.Triple<android.graphics.PointF, java.lang.Float, java.lang.Boolean> component19() {
        return null;
    }
    
    /**
     * 🎯 根据三个内圆点计算圆心和半径 - 使用视图坐标（基于ThreeCircleMeasureHelper算法）
     */
    @org.jetbrains.annotations.NotNull
    public final kotlin.Triple<android.graphics.PointF, java.lang.Float, java.lang.Boolean> calculateInnerCircleFromThreePoints() {
        return null;
    }
    
    /**
     * 🎯 三点确定圆心算法 - 基于ThreeCircleMeasureHelper实现
     */
    private final kotlin.Triple<android.graphics.PointF, java.lang.Float, java.lang.Boolean> calculateCircleFromThreePoints(android.graphics.PointF p1, android.graphics.PointF p2, android.graphics.PointF p3) {
        return null;
    }
    
    /**
     * 🎯 计算内圆半径（使用位图坐标，基于三点圆算法）
     */
    public final double calculateInnerRadius() {
        return 0.0;
    }
    
    /**
     * 🎯 基于位图坐标的三点圆半径计算
     */
    private final double calculateCircleFromBitmapPoints(android.graphics.PointF p1, android.graphics.PointF p2, android.graphics.PointF p3) {
        return 0.0;
    }
    
    /**
     * 🎯 计算外圆半径（使用位图坐标）
     */
    public final double calculateOuterRadius() {
        return 0.0;
    }
    
    /**
     * 🎯 计算内圆视图半径（用于绘制，基于三点圆算法）
     */
    public final float calculateInnerViewRadius() {
        return 0.0F;
    }
    
    /**
     * 🎯 计算外圆视图半径（用于绘制）
     */
    public final float calculateOuterViewRadius() {
        return 0.0F;
    }
    
    /**
     * 🎯 计算内圆面积
     */
    public final double calculateInnerArea() {
        return 0.0;
    }
    
    /**
     * 🎯 计算外圆面积
     */
    public final double calculateOuterArea() {
        return 0.0;
    }
    
    /**
     * 🎯 计算环形面积
     */
    public final double calculateRingArea() {
        return 0.0;
    }
    
    /**
     * 🎯 计算内圆周长
     */
    public final double calculateInnerPerimeter() {
        return 0.0;
    }
    
    /**
     * 🎯 计算外圆周长
     */
    public final double calculateOuterPerimeter() {
        return 0.0;
    }
    
    /**
     * 🎯 检查点是否在触摸范围内 - 直接在视图坐标系中检测
     */
    public final boolean isPointInTouchRange(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint, float touchRadius) {
        return false;
    }
    
    /**
     * 🎯 检查特定点是否在触摸范围内
     */
    public final boolean isPointInTouchRange(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint, int pointIndex, float touchRadius) {
        return false;
    }
    
    /**
     * 🎯 获取最近的控制点索引
     */
    public final int getNearestPointIndex(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return 0;
    }
    
    /**
     * 🎯 三点双圆约束更新方法 - 基于三点圆算法的核心约束逻辑
     * 控制点索引：[0,1,2]=内圆三点, [3]=外圆半径点, [4]=计算圆心
     */
    public final void updateWithThreeTwoCirclesConstraint(int pointIndex, @org.jetbrains.annotations.NotNull
    android.graphics.PointF newPoint) {
    }
    
    /**
     * 🎯 验证外圆约束 - 确保外圆半径不小于内圆半径
     */
    private final void validateOuterCircleConstraint() {
    }
    
    /**
     * 🎯 自动修正共线点 - 轻微偏移使三点不共线
     */
    private final void autoCorrectCollinearPoints(int draggedPointIndex) {
    }
    
    /**
     * 🔄 坐标转换方法 - 视图坐标转位图坐标
     */
    private final android.graphics.PointF convertViewToBitmapCoords(android.graphics.PointF viewPoint, android.widget.ImageView imageView) {
        return null;
    }
    
    /**
     * 🔄 坐标转换方法 - 位图坐标转视图坐标
     */
    private final android.graphics.PointF convertBitmapToViewCoords(android.graphics.PointF bitmapPoint, android.widget.ImageView imageView) {
        return null;
    }
    
    /**
     * 🔄 同步位图坐标 - 将视图坐标转换为位图坐标
     */
    public final void syncBitmapCoords(@org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView) {
    }
    
    /**
     * 🔄 同步视图坐标 - 将位图坐标转换为视图坐标（缩放时调用）
     */
    public final void syncViewCoords(@org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView) {
    }
    
    /**
     * 🎯 计算文本显示位置（基于计算得出的圆心）
     */
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF calculateTextPosition() {
        return null;
    }
    
    /**
     * 🎯 获取双圆的显示文本
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDisplayText() {
        return null;
    }
    
    /**
     * 🔄 标记为已修改
     */
    private final void markAsModified() {
    }
    
    /**
     * 🔄 清除缓存
     */
    private final void invalidateCache() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0006\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeTwoCirclesMeasurement$Companion;", "", "()V", "CACHE_VALIDITY_MS", "", "PI", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}