package com.touptek.measurerealize.utils

import android.graphics.Bitmap
import android.graphics.PointF
import android.util.Log
import android.view.MotionEvent
import com.touptek.measurerealize.TpImageView
import kotlin.math.*

/**
 * 🎯 三点双圆测量助手 - 基于三点圆算法的双圆测量实现
 * 核心逻辑：通过三个内圆控制点计算圆心，外圆与内圆同心
 */
class ThreeTwoCirclesMeasureHelper {
    companion object {
        private const val TAG = "ThreeTwoCirclesMeasureHelper"
        private const val TOUCH_RADIUS = 80f
        private const val LONG_PRESS_DURATION = 800L
        private const val CLICK_TOLERANCE = 30f
    }

    // 核心组件
    private lateinit var imageView: TpImageView
    private lateinit var bitmap: Bitmap
    private var measurementUpdateCallback: (() -> Unit)? = null

    // 测量数据
    private val measurements = mutableListOf<ThreeTwoCirclesMeasurement>()
    private var selectedMeasurement: ThreeTwoCirclesMeasurement? = null
    private var activeMeasurement: ThreeTwoCirclesMeasurement? = null

    // 交互状态管理
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    /**
     * 🚀 初始化助手
     */
    fun init(imageView: TpImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.bitmap = bitmap
        Log.d(TAG, "🚀 ThreeTwoCirclesMeasureHelper initialized")
    }

    /**
     * 🔄 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🎯 开始新的三点双圆测量 - 在屏幕中央创建基于三点圆的双圆
     */
    fun startNewMeasurement(): String {
        // 在视图中心生成三点双圆
        val viewCenterX = imageView.width / 2f
        val viewCenterY = imageView.height / 2f
        val baseRadius = minOf(imageView.width, imageView.height) / 12f

        // 创建三个内圆控制点（等边三角形分布，便于形成有效圆）
        val innerPoints = mutableListOf<PointF>()
        val angles = listOf(0f, 120f, 240f)
        for (angle in angles) {
            val radian = Math.toRadians(angle.toDouble())
            innerPoints.add(PointF(
                viewCenterX + (baseRadius * cos(radian)).toFloat(),
                viewCenterY + (baseRadius * sin(radian)).toFloat()
            ))
        }

        // 外圆控制点（半径比内圆大50%）
        val outerRadius = baseRadius * 1.5f
        val outerPoint = PointF(viewCenterX + outerRadius, viewCenterY)

        // 计算圆心（应该接近视图中心）
        val (calculatedCenter, calculatedRadius, isValid) = calculateCircleFromThreePoints(
            innerPoints[0], innerPoints[1], innerPoints[2]
        )

        val center = if (isValid) calculatedCenter else PointF(viewCenterX, viewCenterY)

        Log.d(TAG, "🎯 Calculated center: (${center.x}, ${center.y}), radius: $calculatedRadius, isValid: $isValid")

        // 创建完整的测量
        val measurement = ThreeTwoCirclesMeasurement(
            isSelected = true,
            isEditing = false,  // 直接设为完成状态，可拖动
            isCompleted = true
        )

        // 添加视图坐标点：[内圆点1, 内圆点2, 内圆点3, 外圆半径点, 计算圆心]
        measurement.viewPoints.addAll(innerPoints + listOf(outerPoint, center))

        // 同步位图坐标
        measurement.syncBitmapCoords(imageView)

        // 设置文本位置
        measurement.textPosition = measurement.calculateTextPosition()

        // 取消其他测量的选中状态
        measurements.forEach {
            it.isSelected = false
            it.isEditing = false
        }

        measurements.add(measurement)
        activeMeasurement = null
        selectedMeasurement = measurement

        notifyUpdate()
        Log.d(TAG, "🎯 Created new three two circles measurement - inner: ${String.format("%.1f", measurement.calculateInnerRadius())}px, outer: ${String.format("%.1f", measurement.calculateOuterRadius())}px")
        return measurement.id
    }

    /**
     * 🎯 三点确定圆心算法 - 复用ThreeTwoCirclesMeasurement中的实现
     */
    private fun calculateCircleFromThreePoints(p1: PointF, p2: PointF, p3: PointF): Triple<PointF, Float, Boolean> {
        // 检查三点是否共线
        val area = abs((p2.x - p1.x) * (p3.y - p1.y) - (p3.x - p1.x) * (p2.y - p1.y))
        if (area < 1e-6) {
            return Triple(PointF(), 0f, false) // 三点共线，无法确定圆
        }

        // 计算圆心坐标
        val d = 2 * (p1.x * (p2.y - p3.y) + p2.x * (p3.y - p1.y) + p3.x * (p1.y - p2.y))
        if (abs(d) < 1e-6) {
            return Triple(PointF(), 0f, false)
        }

        val ux = ((p1.x * p1.x + p1.y * p1.y) * (p2.y - p3.y) +
                  (p2.x * p2.x + p2.y * p2.y) * (p3.y - p1.y) +
                  (p3.x * p3.x + p3.y * p3.y) * (p1.y - p2.y)) / d

        val uy = ((p1.x * p1.x + p1.y * p1.y) * (p3.x - p2.x) +
                  (p2.x * p2.x + p2.y * p2.y) * (p1.x - p3.x) +
                  (p3.x * p3.x + p3.y * p3.y) * (p2.x - p1.x)) / d

        val center = PointF(ux, uy)
        val radius = sqrt((center.x - p1.x).pow(2) + (center.y - p1.y).pow(2))

        return Triple(center, radius, true)
    }

    /**
     * 🎯 获取所有测量数据用于覆盖层显示
     */
    fun getAllMeasurementData(): List<ThreeTwoCirclesMeasurement> {
        return measurements.toList()
    }

    /**
     * 🔍 调试方法：打印所有测量对象的状态
     */
    private fun debugMeasurementStates() {
        Log.d(TAG, "🔍 === Measurement States Debug ===")
        measurements.forEachIndexed { index, measurement ->
            Log.d(TAG, "🔍 Measurement $index: id=${measurement.id}, isSelected=${measurement.isSelected}, viewPoints.size=${measurement.viewPoints.size}")
            measurement.viewPoints.forEachIndexed { pointIndex, point ->
                Log.d(TAG, "🔍   Point $pointIndex: (${point.x}, ${point.y})")
            }
        }
        Log.d(TAG, "🔍 selectedMeasurement: ${selectedMeasurement?.id}")
        Log.d(TAG, "🔍 === End Debug ===")
    }

    /**
     * 🎯 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 🎯 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean {
        val hasSelected = selectedMeasurement != null
        Log.d(TAG, "🔍 hasSelectedMeasurement called: $hasSelected, selectedMeasurement: ${selectedMeasurement?.id}, measurements count: ${measurements.size}")
        return hasSelected
    }

    /**
     * 🎯 获取选中的测量
     */
    fun getSelectedMeasurement(): ThreeTwoCirclesMeasurement? = selectedMeasurement

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean = isDraggingPoint

    /**
     * 🎯 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            for (pointIndex in 0..4) { // 三点双圆有5个点：内圆点1-3(0-2)、外圆点(3)、圆心(4)
                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                    Log.d(TAG, "🎯 Found nearby measurement ${measurement.id} at point index $pointIndex")
                    return true
                }
            }
            false
        }
    }

    /**
     * 🎯 检查点是否在测量上（用于混合触摸处理器）
     */
    fun isPointOnMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            for (pointIndex in 0..4) { // 三点双圆有5个点：内圆点1-3(0-2)、外圆点(3)、圆心(4)
                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                    Log.d(TAG, "🎯 Point on measurement ${measurement.id} at point index $pointIndex")
                    return true
                }
            }
            false
        }
    }

    /**
     * 🎯 清除所有选中状态
     */
    fun clearSelection() {
        Log.d(TAG, "🔄 Clearing all selections, measurements count: ${measurements.size}")
        measurements.forEach {
            Log.d(TAG, "🔄 Clearing selection for measurement ${it.id}")
            it.isSelected = false
        }
        selectedMeasurement = null
        notifyUpdate()
        Log.d(TAG, "✅ All selections cleared")
    }

    /**
     * 🎯 删除选中的测量 - 智能删除逻辑
     */
    fun deleteSelectedMeasurement(): Boolean {
        Log.d(TAG, "🗑️ deleteSelectedMeasurement called - selectedMeasurement: ${selectedMeasurement?.id}, measurements count: ${measurements.size}")
        var selected = selectedMeasurement

        // 🔄 如果没有选中的测量，尝试自动选中最后一个测量
        if (selected == null && measurements.isNotEmpty()) {
            val lastMeasurement = measurements.last()
            lastMeasurement.isSelected = true
            selectedMeasurement = lastMeasurement
            selected = lastMeasurement
            Log.d(TAG, "🔄 Auto-selected last measurement for deletion: ${lastMeasurement.id}")
        }

        if (selected == null) {
            Log.w(TAG, "⚠️ No measurements available for deletion")
            return false
        }

        Log.d(TAG, "🗑️ Attempting to remove measurement: ${selected.id}")
        val removed = measurements.remove(selected)
        Log.d(TAG, "🗑️ Removal result: $removed, measurements count after removal: ${measurements.size}")

        if (removed) {
            Log.d(TAG, "🗑️ Measurement removed successfully, clearing selectedMeasurement")
            selectedMeasurement = null
            Log.d(TAG, "🗑️ Resetting interaction state")
            resetInteractionState()

            // 如果还有其他测量，选中最后一个
            if (measurements.isNotEmpty()) {
                val lastMeasurement = measurements.last()
                lastMeasurement.isSelected = true
                selectedMeasurement = lastMeasurement
                Log.d(TAG, "🎯 Auto-selected last measurement: ${lastMeasurement.id}")
            } else {
                Log.d(TAG, "🗑️ No more measurements left after deletion")
            }

            Log.d(TAG, "🗑️ About to call notifyUpdate()")
            notifyUpdate()
            Log.d(TAG, "✅ Three two circles measurement deleted: ${selected.id}")
            return true
        } else {
            Log.e(TAG, "❌ Failed to remove measurement: ${selected.id}")
        }
        return false
    }

    /**
     * 🔄 重置交互状态
     */
    private fun resetInteractionState() {
        isDraggingPoint = false
        draggedPointIndex = -1
        longPressStartTime = 0L
    }

    /**
     * 🔔 通知更新
     */
    private fun notifyUpdate() {
        Log.d(TAG, "🔔 notifyUpdate called, callback: ${measurementUpdateCallback != null}")
        try {
            measurementUpdateCallback?.invoke()
            Log.d(TAG, "🔔 notifyUpdate callback invoked successfully")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in notifyUpdate callback: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 🎯 暂停测量 - 保持数据但停止交互
     */
    fun pauseMeasurement() {
        Log.d(TAG, "⏸️ Measurement paused - data preserved")
    }

    /**
     * 🔄 缩放变化时同步坐标
     */
    fun onScaleChanged() {
        Log.d(TAG, "🔄 Scale changed - syncing ${measurements.size} three two circles measurements")
        // 从位图坐标恢复视图坐标，确保测量跟随图像
        measurements.forEach { measurement ->
            Log.d(TAG, "🔄 Before sync: viewPoints size=${measurement.viewPoints.size}")
            measurement.syncViewCoords(imageView)
            measurement.textPosition = measurement.calculateTextPosition()
            Log.d(TAG, "🔄 After sync: viewPoints updated for measurement ${measurement.id}")
        }
        notifyUpdate()
        Log.d(TAG, "🔄 Three two circles coordinate sync completed")
    }

    /**
     * 🎯 处理触摸事件 - 核心交互逻辑
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 记录触摸起始信息
                longPressStartTime = System.currentTimeMillis()
                lastTouchX = x
                lastTouchY = y
                Log.d(TAG, "🎯 ACTION_DOWN at (${x}, ${y}), measurements count: ${measurements.size}")

                // 检查端点触摸（优先级最高）
                for (measurement in measurements.reversed()) {
                    Log.d(TAG, "🔍 Checking measurement ${measurement.id}, isSelected: ${measurement.isSelected}")
                    if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                        selectedMeasurement = measurement
                        draggedPointIndex = measurement.getNearestPointIndex(touchPoint)
                        isDraggingPoint = true

                        // 设置选中状态（不设置编辑状态）
                        measurements.forEach { it.isSelected = false }
                        measurement.isSelected = true

                        notifyUpdate()
                        Log.d(TAG, "🎯 Started dragging point $draggedPointIndex of measurement ${measurement.id}")
                        return true
                    }
                }

                // 检查是否触摸到任何测量对象（不仅仅是控制点）
                var touchedAnyMeasurement = false
                for (measurement in measurements) {
                    if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                        touchedAnyMeasurement = true
                        Log.d(TAG, "🎯 Detected touch on measurement ${measurement.id} (not control point)")
                        break
                    }
                }

                // 处理空白区域点击 - 智能UI区域保护
                if (!touchedAnyMeasurement && isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
                    measurements.forEach { it.isSelected = false }
                    selectedMeasurement = null
                    Log.d(TAG, "🔄 Cleared all selections - empty area clicked in image content")
                    debugMeasurementStates()
                    notifyUpdate()
                } else if (touchedAnyMeasurement) {
                    Log.d(TAG, "🎯 Touched measurement detected, preserving selection state for ACTION_UP processing")
                } else {
                    Log.d(TAG, "🛡️ Clicked in UI area, preserving selection state")
                }

                return false // 让ImageView处理缩放
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDraggingPoint && selectedMeasurement != null && draggedPointIndex >= 0) {
                    // 正常拖拽处理
                    selectedMeasurement!!.updateWithThreeTwoCirclesConstraint(draggedPointIndex, touchPoint)
                    selectedMeasurement!!.syncBitmapCoords(imageView)
                    notifyUpdate()
                    return true
                } else if (!isDraggingPoint && selectedMeasurement != null) {
                    // 触摸恢复机制
                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS) && measurement == selectedMeasurement) {
                            draggedPointIndex = measurement.getNearestPointIndex(touchPoint)
                            isDraggingPoint = true
                            // 立即处理这次移动
                            selectedMeasurement!!.updateWithThreeTwoCirclesConstraint(draggedPointIndex, touchPoint)
                            selectedMeasurement!!.syncBitmapCoords(imageView)
                            notifyUpdate()
                            Log.d(TAG, "🔄 Touch recovery successful - resumed dragging point $draggedPointIndex")
                            return true
                        }
                    }
                }
                return false
            }

            MotionEvent.ACTION_UP -> {
                val touchDuration = System.currentTimeMillis() - longPressStartTime
                val touchDistance = sqrt((x - lastTouchX) * (x - lastTouchX) + (y - lastTouchY) * (y - lastTouchY))
                val wasDragging = isDraggingPoint

                var handled = false

                // 重置拖拽状态
                isDraggingPoint = false
                draggedPointIndex = -1

                if (wasDragging) {
                    // 拖拽完成
                    notifyUpdate()
                    handled = true
                    Log.d(TAG, "✅ Dragging completed")
                }

                // 长按删除
                if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                            measurements.remove(measurement)
                            if (selectedMeasurement == measurement) {
                                selectedMeasurement = null
                            }
                            notifyUpdate()
                            handled = true
                            Log.d(TAG, "🗑️ Long press delete: ${measurement.id}")
                            break
                        }
                    }
                }

                // 轻触选中
                if (!wasDragging && !handled && touchDistance < CLICK_TOLERANCE && touchDuration < LONG_PRESS_DURATION) {
                    Log.d(TAG, "🎯 Attempting light touch selection at (${touchPoint.x}, ${touchPoint.y})")
                    debugMeasurementStates()
                    for (measurement in measurements.reversed()) {
                        Log.d(TAG, "🔍 Checking measurement ${measurement.id} for light touch selection")
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                            measurements.forEach { it.isSelected = false }
                            measurement.isSelected = true
                            selectedMeasurement = measurement
                            notifyUpdate()
                            Log.d(TAG, "🎯 Selected measurement: ${measurement.id}")
                            debugMeasurementStates()
                            return true
                        } else {
                            Log.d(TAG, "🔍 Measurement ${measurement.id} not in touch range")
                        }
                    }
                    Log.d(TAG, "⚠️ No measurement found for light touch selection")
                    // 尝试更宽松的检测范围
                    val expandedRadius = TOUCH_RADIUS * 1.5f
                    Log.d(TAG, "🔍 Trying expanded touch radius: $expandedRadius")
                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, expandedRadius)) {
                            measurements.forEach { it.isSelected = false }
                            measurement.isSelected = true
                            selectedMeasurement = measurement
                            notifyUpdate()
                            Log.d(TAG, "🎯 Selected measurement with expanded radius: ${measurement.id}")
                            debugMeasurementStates()
                            return true
                        }
                    }
                    Log.d(TAG, "⚠️ Still no measurement found even with expanded radius")
                }

                return handled
            }
        }
        return false
    }

    /**
     * 🔧 智能空白区域检测 - 避免UI按钮区域被误判
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in isInImageContentArea: ${e.message}")
            // 出错时保守处理，不清除选中状态
            return false
        }
    }

    /**
     * 🧹 清理资源
     */
    fun cleanup() {
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
        Log.d(TAG, "🧹 ThreeTwoCirclesMeasureHelper cleaned up")
    }
}
