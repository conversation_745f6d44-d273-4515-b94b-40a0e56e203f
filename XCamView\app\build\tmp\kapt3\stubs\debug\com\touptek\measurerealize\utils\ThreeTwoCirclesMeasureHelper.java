package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 三点双圆测量助手 - 基于三点圆算法的双圆测量实现
 * 核心逻辑：通过三个内圆控制点计算圆心，外圆与内圆同心
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010!\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u0000 82\u00020\u0001:\u00018B\u0005\u00a2\u0006\u0002\u0010\u0002J2\u0010\u0018\u001a\u0014\u0012\u0004\u0012\u00020\u001a\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\f0\u00192\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001c\u001a\u00020\u001a2\u0006\u0010\u001d\u001a\u00020\u001aH\u0002J\u0006\u0010\u001e\u001a\u00020\u0014J\u0006\u0010\u001f\u001a\u00020\u0014J\b\u0010 \u001a\u00020\u0014H\u0002J\u0006\u0010!\u001a\u00020\fJ\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00040#J\u0006\u0010$\u001a\u00020\bJ\b\u0010%\u001a\u0004\u0018\u00010\u0004J\u001e\u0010&\u001a\u00020\f2\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020\b2\u0006\u0010*\u001a\u00020\bJ\u0006\u0010+\u001a\u00020\fJ\u0016\u0010,\u001a\u00020\u00142\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006J\u0006\u0010\u000b\u001a\u00020\fJ \u0010-\u001a\u00020\f2\u0006\u0010.\u001a\u00020\u001a2\u0006\u0010)\u001a\u00020\b2\u0006\u0010*\u001a\u00020\bH\u0002J\u000e\u0010/\u001a\u00020\f2\u0006\u0010.\u001a\u00020\u001aJ\u000e\u00100\u001a\u00020\f2\u0006\u0010.\u001a\u00020\u001aJ\b\u00101\u001a\u00020\u0014H\u0002J\u0006\u00102\u001a\u00020\u0014J\b\u00103\u001a\u00020\u0014H\u0002J\u0014\u00104\u001a\u00020\u00142\f\u00105\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013J\u0006\u00106\u001a\u000207R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0012\u001a\n\u0012\u0004\u0012\u00020\u0014\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00069"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeTwoCirclesMeasureHelper;", "", "()V", "activeMeasurement", "Lcom/touptek/measurerealize/utils/ThreeTwoCirclesMeasurement;", "bitmap", "Landroid/graphics/Bitmap;", "draggedPointIndex", "", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "isDraggingPoint", "", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "", "selectedMeasurement", "calculateCircleFromThreePoints", "Lkotlin/Triple;", "Landroid/graphics/PointF;", "p1", "p2", "p3", "cleanup", "clearSelection", "debugMeasurementStates", "deleteSelectedMeasurement", "getAllMeasurementData", "", "getMeasurementCount", "getSelectedMeasurement", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "hasSelectedMeasurement", "init", "isInImageContentArea", "touchPoint", "isNearAnyMeasurement", "isPointOnMeasurement", "notifyUpdate", "pauseMeasurement", "resetInteractionState", "setMeasurementUpdateCallback", "callback", "startNewMeasurement", "", "Companion", "app_debug"})
public final class ThreeTwoCirclesMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.ThreeTwoCirclesMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "ThreeTwoCirclesMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private static final float CLICK_TOLERANCE = 30.0F;
    private com.touptek.measurerealize.TpImageView imageView;
    private android.graphics.Bitmap bitmap;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    private final java.util.List<com.touptek.measurerealize.utils.ThreeTwoCirclesMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.ThreeTwoCirclesMeasurement selectedMeasurement;
    private com.touptek.measurerealize.utils.ThreeTwoCirclesMeasurement activeMeasurement;
    private boolean isDraggingPoint = false;
    private int draggedPointIndex = -1;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    
    public ThreeTwoCirclesMeasureHelper() {
        super();
    }
    
    /**
     * 🚀 初始化助手
     */
    public final void init(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🔄 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 开始新的三点双圆测量 - 在屏幕中央创建基于三点圆的双圆
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🎯 三点确定圆心算法 - 复用ThreeTwoCirclesMeasurement中的实现
     */
    private final kotlin.Triple<android.graphics.PointF, java.lang.Float, java.lang.Boolean> calculateCircleFromThreePoints(android.graphics.PointF p1, android.graphics.PointF p2, android.graphics.PointF p3) {
        return null;
    }
    
    /**
     * 🎯 获取所有测量数据用于覆盖层显示
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.ThreeTwoCirclesMeasurement> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🔍 调试方法：打印所有测量对象的状态
     */
    private final void debugMeasurementStates() {
    }
    
    /**
     * 🎯 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🎯 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🎯 获取选中的测量
     */
    @org.jetbrains.annotations.Nullable
    public final com.touptek.measurerealize.utils.ThreeTwoCirclesMeasurement getSelectedMeasurement() {
        return null;
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🎯 检查是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 检查点是否在测量上（用于混合触摸处理器）
     */
    public final boolean isPointOnMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 清除所有选中状态
     */
    public final void clearSelection() {
    }
    
    /**
     * 🎯 删除选中的测量 - 智能删除逻辑
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔄 重置交互状态
     */
    private final void resetInteractionState() {
    }
    
    /**
     * 🔔 通知更新
     */
    private final void notifyUpdate() {
    }
    
    /**
     * 🎯 暂停测量 - 保持数据但停止交互
     */
    public final void pauseMeasurement() {
    }
    
    /**
     * 🎯 处理触摸事件 - 核心交互逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🔧 智能空白区域检测 - 避免UI按钮区域被误判
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🧹 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeTwoCirclesMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}